'use client'

import { useState } from 'react'
import Image from 'next/image'

export default function OptimizedImage({ 
  src, 
  alt, 
  fill = false, 
  width, 
  height, 
  className = '', 
  priority = false,
  ...props 
}) {
  const [imgSrc, setImgSrc] = useState(src)
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)

  const handleError = () => {
    setHasError(true)
    setImgSrc('/images/placeholder.svg')
  }

  const handleLoad = () => {
    setIsLoading(false)
  }

  return (
    <div className={className}>
      {isLoading && !hasError && (
        <div className="absolute inset-0 bg-gray-200 dark:bg-gray-700 animate-pulse rounded" />
      )}
      
      <Image
        src={imgSrc}
        alt={alt}
        fill={fill}
        width={width}
        height={height}
        className={className}
        priority={priority}
        onError={handleError}
        onLoad={handleLoad}
        {...props}
      />
      
      {hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800 text-gray-500 text-sm">
          Imagen no disponible
        </div>
      )}
    </div>
  )
}