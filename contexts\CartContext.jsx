'use client'

import { createContext, useContext, useReducer, useEffect, useState } from 'react'
import { getProductById } from '@/lib/data/products'
import { loadRealProduct } from '@/lib/real-products-loader'

// Cart Context
const CartContext = createContext()

// Cart Actions
const CART_ACTIONS = {
  ADD_ITEM: 'ADD_ITEM',
  REMOVE_ITEM: 'REMOVE_ITEM',
  UPDATE_QUANTITY: 'UPDATE_QUANTITY',
  CLEAR_CART: 'CLEAR_CART',
  LOAD_CART: 'LOAD_CART',
  APPLY_DISCOUNT: 'APPLY_DISCOUNT',
  REMOVE_DISCOUNT: 'REMOVE_DISCOUNT'
}

// Cart Reducer
const cartReducer = (state, action) => {
  console.log('🛒 cartReducer called with action:', action.type, action.payload)
  console.log('🛒 cartReducer current state.items:', state.items)

  switch (action.type) {
    case CART_ACTIONS.ADD_ITEM: {
      const { productId, size, quantity = 1 } = action.payload
      // Note: Product validation is now done in addItem function before dispatching
      // So we can proceed with adding the item to cart
      
      const existingItemIndex = state.items.findIndex(
        item => item.productId === productId && item.size === size
      )
      
      if (existingItemIndex >= 0) {
        // Update existing item quantity
        const updatedItems = [...state.items]
        updatedItems[existingItemIndex].quantity += quantity
        
        const newState = {
          ...state,
          items: updatedItems,
          updatedAt: new Date().toISOString()
        }
        console.log('🛒 cartReducer ADD_ITEM (existing) new state.items:', newState.items)
        return newState
      } else {
        // Add new item
        const { product } = action.payload
        const newItem = {
          id: `${productId}-${size}`,
          productId,
          name: product?.name || 'Unknown Product',
          brand: product?.brand || 'Unknown Brand',
          image: product?.images?.[0] || '/placeholder-shoe.jpg',
          size,
          quantity,
          price: product?.price || 0,
          addedAt: new Date().toISOString()
        }
        
        const newState = {
          ...state,
          items: [...state.items, newItem],
          updatedAt: new Date().toISOString()
        }
        console.log('🛒 cartReducer ADD_ITEM (new) new state.items:', newState.items)
        return newState
      }
    }
    
    case CART_ACTIONS.REMOVE_ITEM: {
      const { itemId } = action.payload
      
      return {
        ...state,
        items: state.items.filter(item => item.id !== itemId),
        updatedAt: new Date().toISOString()
      }
    }
    
    case CART_ACTIONS.UPDATE_QUANTITY: {
      const { itemId, quantity } = action.payload
      
      if (quantity <= 0) {
        return cartReducer(state, {
          type: CART_ACTIONS.REMOVE_ITEM,
          payload: { itemId }
        })
      }
      
      const updatedItems = state.items.map(item =>
        item.id === itemId ? { ...item, quantity } : item
      )
      
      return {
        ...state,
        items: updatedItems,
        updatedAt: new Date().toISOString()
      }
    }
    
    case CART_ACTIONS.CLEAR_CART: {
      return {
        ...initialCartState,
        updatedAt: new Date().toISOString()
      }
    }
    
    case CART_ACTIONS.LOAD_CART: {
      return {
        ...state,
        ...action.payload
      }
    }
    
    case CART_ACTIONS.APPLY_DISCOUNT: {
      const { code, discount } = action.payload
      
      return {
        ...state,
        discount: {
          code,
          type: discount.type, // 'percentage' or 'fixed'
          value: discount.value,
          appliedAt: new Date().toISOString()
        },
        updatedAt: new Date().toISOString()
      }
    }
    
    case CART_ACTIONS.REMOVE_DISCOUNT: {
      return {
        ...state,
        discount: null,
        updatedAt: new Date().toISOString()
      }
    }
    
    default:
      return state
  }
}

// Initial Cart State
const initialCartState = {
  items: [],
  discount: null,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
}

// Cart Provider Component
export function CartProvider({ children }) {
  const [state, dispatch] = useReducer(cartReducer, initialCartState)
  const [isHydrated, setIsHydrated] = useState(false)

  // Hydration effect
  useEffect(() => {
    setIsHydrated(true)
  }, [])

  // Load cart from localStorage on mount (only after hydration)
  useEffect(() => {
    if (!isHydrated) return

    try {
      const savedCart = localStorage.getItem('twl-cart')
      if (savedCart) {
        const parsedCart = JSON.parse(savedCart)
        dispatch({
          type: CART_ACTIONS.LOAD_CART,
          payload: parsedCart
        })
      }
    } catch (error) {
      console.error('Error loading cart from localStorage:', error)
    }
  }, [isHydrated])

  // Save cart to localStorage whenever it changes (only after hydration)
  useEffect(() => {
    if (!isHydrated) return

    try {
      localStorage.setItem('twl-cart', JSON.stringify(state))
    } catch (error) {
      console.error('Error saving cart to localStorage:', error)
    }
  }, [isHydrated, state])
  
  // Enhanced product finder - now uses direct products (no path conversion!)
  const findProduct = async (productId) => {
    console.log('🔍 Finding product with ID:', productId)

    // First try static products (fastest)
    const staticProduct = getProductById(productId)
    if (staticProduct) {
      console.log('✅ Found static product:', staticProduct.name)
      return staticProduct
    }

    // Try direct product loader (no path conversion needed!)
    try {
      const { loadProductDirect } = await import('@/lib/direct-products-loader')
      const directProduct = await loadProductDirect(productId)
      if (directProduct) {
        console.log('✅ Found direct product:', directProduct.name)
        return directProduct
      }
    } catch (error) {
      console.log('⚠️ Direct product loader failed:', error.message)
    }

    // Fallback to real product loader (with path conversion)
    try {
      const realProduct = await loadRealProduct(productId)
      if (realProduct) {
        console.log('✅ Found real product:', realProduct.name)
        return realProduct
      }
    } catch (error) {
      console.log('⚠️ Real product loader failed:', error.message)
    }

    console.log('❌ Product not found anywhere:', productId)
    return null
  }

  // Cart Actions
  const addItem = async (productId, size, quantity = 1) => {
    console.log('Cart addItem called with:', { productId, size, quantity }) // Debug log
    const product = await findProduct(productId)
    console.log('Found product:', product) // Debug log
    console.log('Product images:', product?.images) // Debug log
    console.log('Product first image:', product?.images?.[0]) // Debug log

    if (!product) {
      console.error('❌ Cannot add item: Product not found')
      return
    }

    dispatch({
      type: CART_ACTIONS.ADD_ITEM,
      payload: { productId, size, quantity, product }
    })
  }
  
  const removeItem = (itemId) => {
    dispatch({
      type: CART_ACTIONS.REMOVE_ITEM,
      payload: { itemId }
    })
  }
  
  const updateQuantity = (itemId, quantity) => {
    dispatch({
      type: CART_ACTIONS.UPDATE_QUANTITY,
      payload: { itemId, quantity }
    })
  }
  
  const clearCart = () => {
    dispatch({ type: CART_ACTIONS.CLEAR_CART })
  }
  
  const applyDiscount = (code, discount) => {
    dispatch({
      type: CART_ACTIONS.APPLY_DISCOUNT,
      payload: { code, discount }
    })
  }
  
  const removeDiscount = () => {
    dispatch({ type: CART_ACTIONS.REMOVE_DISCOUNT })
  }
  
  // Cart Calculations
  const getItemsCount = () => {
    console.log('🛒 getItemsCount called, state.items:', state.items)
    const count = state.items.reduce((total, item) => total + item.quantity, 0)
    console.log('🛒 getItemsCount returning:', count)
    return count
  }
  
  const getSubtotal = () => {
    return state.items.reduce((total, item) => {
      const product = getProductById(item.productId)
      return total + (product ? product.price * item.quantity : 0)
    }, 0)
  }
  
  const getDiscountAmount = () => {
    if (!state.discount) return 0
    
    const subtotal = getSubtotal()
    
    if (state.discount.type === 'percentage') {
      return subtotal * (state.discount.value / 100)
    } else if (state.discount.type === 'fixed') {
      return Math.min(state.discount.value, subtotal)
    }
    
    return 0
  }
  
  const getTax = () => {
    // 16% IVA for Mexico
    const taxableAmount = getSubtotal() - getDiscountAmount()
    return taxableAmount * 0.16
  }
  
  const getShipping = () => {
    const subtotal = getSubtotal()
    // Free shipping over $3000 MXN
    return subtotal >= 3000 ? 0 : 200
  }
  
  const getTotal = () => {
    return getSubtotal() - getDiscountAmount() + getTax() + getShipping()
  }
  
  const getCartSummary = () => {
    return {
      itemsCount: getItemsCount(),
      subtotal: getSubtotal(),
      discount: getDiscountAmount(),
      tax: getTax(),
      shipping: getShipping(),
      total: getTotal()
    }
  }
  
  // Get cart items with product details
  const getCartItems = () => {
    console.log('🛒 getCartItems called, state.items:', state.items)
    return state.items.map(item => {
      console.log('🛒 Processing cart item:', item)
      // Since we now store product data directly in the cart item, we don't need to look it up
      return {
        ...item,
        product: {
          id: item.productId,
          name: item.name,
          brand: item.brand,
          image: item.image,
          price: item.price
        }
      }
    })
  }

  // Get order history (mock data for demo)
  const getOrderHistory = () => {
    // For now, return mock data - in real app, check authentication
    // if (!isAuthenticated) return []

    // Mock order history
    return [
      {
        id: 'ORD-001',
        date: '2024-01-15T10:00:00Z',
        status: 'Entregado',
        items: [
          {
            id: '1',
            name: 'Air Jordan 1 Retro High OG',
            size: '9',
            quantity: 1,
            price: 3500
          }
        ],
        subtotal: 3500,
        tax: 560,
        shipping: 0,
        total: 4060
      }
    ]
  }

  // Process checkout
  const processCheckout = async (checkoutData) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Create order
      const order = {
        id: `ORD-${Date.now()}`,
        date: new Date().toISOString(),
        status: 'Procesando',
        items: state.items.map(item => ({
          id: item.id,
          name: item.name,
          brand: item.brand,
          size: item.size,
          quantity: item.quantity,
          price: item.price
        })),
        subtotal: getSubtotal(),
        tax: getTax(),
        shipping: getShipping(),
        discount: getDiscountAmount(),
        total: getTotal(),
        shippingAddress: checkoutData.shippingAddress,
        billingAddress: checkoutData.billingAddress,
        paymentMethod: checkoutData.paymentMethod
      }

      // Clear cart after successful checkout
      dispatch({ type: CART_ACTIONS.CLEAR_CART })

      return { success: true, order }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }
  
  const value = {
    // State
    cart: state,
    items: getCartItems(),
    summary: getCartSummary(),

    // Actions
    addItem,
    removeItem,
    updateQuantity,
    clearCart,
    applyDiscount,
    removeDiscount,

    // Utilities
    getItemsCount,
    getSubtotal,
    getDiscountAmount,
    getTax,
    getShipping,
    getTotal,
    getOrderHistory,
    processCheckout
  }



  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  )
}

// Custom hook to use cart context
export function useCart() {
  const context = useContext(CartContext)
  
  if (!context) {
    throw new Error('useCart must be used within a CartProvider')
  }
  
  return context
}
