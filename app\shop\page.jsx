'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { products as mockProducts, brands, categories, filterProducts, sortProducts } from '@/data/products'
import { getAllRealProducts } from '@/lib/real-products-loader'
import AnimatedProductCard from '@/components/ui/AnimatedProductCard'
import MobileProductCard from '@/components/ui/MobileProductCard'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import EnhancedSearchBar from '@/components/features/EnhancedSearchBar'
import Badge from '@/components/ui/Badge'
import { Card, CardContent } from '@/components/ui/Card'
import AuthModal from '@/components/ui/AuthModal'
import EnhancedVoiceSearch from '@/components/features/EnhancedVoiceSearch'
import VisualSearch from '@/components/features/VisualSearch'
import { MobileContainer, MobileSection, MobileGrid, MobileCard, MobileButton, MobileBottomSheet } from '@/components/mobile/MobileContainer'

export default function ShopPage() {
  const searchParams = useSearchParams()
  const [products, setProducts] = useState([])
  const [isLoadingProducts, setIsLoadingProducts] = useState(true)
  const [filteredProducts, setFilteredProducts] = useState(mockProducts)
  const [filters, setFilters] = useState({
    search: '',
    brand: '',
    category: '',
    gender: '',
    priceRange: { min: 0, max: 50000 },
    size: '',
    isLimited: false,
    isNew: false
  })
  const [sortBy, setSortBy] = useState('newest')
  const [showFilters, setShowFilters] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [productsPerPage] = useState(12)
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false)
  const [isVoiceSearchOpen, setIsVoiceSearchOpen] = useState(false)
  const [isVisualSearchOpen, setIsVisualSearchOpen] = useState(false)
  const [isMobileFiltersOpen, setIsMobileFiltersOpen] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  // Handle URL parameters (brand filtering from brand showcase)
  useEffect(() => {
    const brandParam = searchParams.get('brand')
    if (brandParam) {
      console.log(`🔗 Brand filter from URL: ${brandParam}`)
      // Map brand slug back to brand name for filtering
      const brandName = brandParam.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
      console.log(`🔗 Mapped brand name: ${brandName}`)

      setFilters(prev => ({
        ...prev,
        brand: brandParam
      }))

      // Show filters sidebar when coming from brand showcase
      setShowFilters(true)
    }
  }, [searchParams])

  // Load real products on mount
  useEffect(() => {
    const loadRealProducts = async () => {
      setIsLoadingProducts(true)
      try {
        const realProducts = getAllRealProducts()
        console.log('Loaded real products for shop:', realProducts.length)
        setProducts(realProducts)
      } catch (error) {
        console.error('Error loading real products:', error)
        setProducts(mockProducts) // Fallback to mock products
      } finally {
        setIsLoadingProducts(false)
      }
    }
    loadRealProducts()
  }, [])

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024)
    }

    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Apply filters and sorting
  useEffect(() => {
    // Use real products if available, otherwise fallback to mock products
    const sourceProducts = products.length > 0 ? products : mockProducts
    let filtered = [...sourceProducts]

    // Apply brand filter
    if (filters.brand) {
      const brandSlug = filters.brand.toLowerCase()
      filtered = filtered.filter(product => {
        const productBrandSlug = product.brand.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '')
        return productBrandSlug === brandSlug
      })
      console.log(`🔍 Brand filter applied: ${filters.brand}, found ${filtered.length} products`)
    }

    // Apply category filter
    if (filters.category) {
      filtered = filtered.filter(product =>
        product.category?.toLowerCase() === filters.category.toLowerCase()
      )
    }

    // Apply search
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase()
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchTerm) ||
        product.brand.toLowerCase().includes(searchTerm) ||
        (product.tags && product.tags.some(tag => tag.toLowerCase().includes(searchTerm)))
      )
    }

    // Apply other filters
    if (filters.gender) {
      filtered = filtered.filter(product =>
        product.gender?.toLowerCase() === filters.gender.toLowerCase()
      )
    }

    if (filters.isLimited) {
      filtered = filtered.filter(product => product.limitedEdition === true)
    }

    if (filters.isNew) {
      filtered = filtered.filter(product => product.isNew === true)
    }

    // Apply price range filter
    if (filters.priceRange.min > 0 || filters.priceRange.max < 50000) {
      filtered = filtered.filter(product =>
        product.price >= filters.priceRange.min && product.price <= filters.priceRange.max
      )
    }

    // Sort products
    filtered = sortProducts(filtered, sortBy)

    setFilteredProducts(filtered)
    setCurrentPage(1) // Reset to first page when filters change
  }, [filters, sortBy, products])

  // Pagination
  const indexOfLastProduct = currentPage * productsPerPage
  const indexOfFirstProduct = indexOfLastProduct - productsPerPage
  const currentProducts = filteredProducts.slice(indexOfFirstProduct, indexOfLastProduct)
  const totalPages = Math.ceil(filteredProducts.length / productsPerPage)

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const clearFilters = () => {
    setFilters({
      search: '',
      brand: '',
      category: '',
      gender: '',
      priceRange: { min: 0, max: 50000 },
      size: '',
      isLimited: false,
      isNew: false
    })
  }

  const activeFiltersCount = Object.values(filters).filter(value => {
    if (typeof value === 'string') return value !== ''
    if (typeof value === 'boolean') return value
    if (typeof value === 'object' && value.min !== undefined) {
      return value.min !== 0 || value.max !== 50000
    }
    return false
  }).length

  const handleVoiceSearchResults = (results, query) => {
    handleFilterChange('search', query)
    setIsVoiceSearchOpen(false)
  }

  const handleVisualSearch = (searchTerm) => {
    // For visual search, we'll use the search term provided
    handleFilterChange('search', searchTerm)
    setIsVisualSearchOpen(false)
  }

  // Mobile Layout
  if (isMobile) {
    return (
      <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine">
        <MobileContainer>
          {/* Mobile Header */}
          <MobileSection
            title="Tienda"
            subtitle="Sneakers de lujo exclusivos"
            className="pt-4"
          >
            {/* Mobile Search */}
            <div className="mb-4">
              <EnhancedSearchBar
                placeholder="Buscar productos... 🎤 📸"
                onSearch={(searchTerm) => handleFilterChange('search', searchTerm)}
              />
            </div>

            {/* Mobile Filter & Sort Bar */}
            <div className="flex gap-2 mb-6">
              <MobileButton
                variant="secondary"
                onClick={() => setIsMobileFiltersOpen(true)}
                className="flex-1"
                icon={
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z" />
                  </svg>
                }
              >
                Filtros {activeFiltersCount > 0 && `(${activeFiltersCount})`}
              </MobileButton>

              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 py-2 glass rounded-xl text-forest-emerald dark:text-light-cloud-gray text-sm focus:outline-none focus:ring-2 focus:ring-rich-gold"
              >
                <option value="newest">Recientes</option>
                <option value="price-low">Precio ↑</option>
                <option value="price-high">Precio ↓</option>
                <option value="rating">Mejor valorados</option>
              </select>
            </div>
          </MobileSection>

          {/* Mobile Products Grid */}
          <MobileContainer noPadding>
            <div className="px-4">
              <p className="text-warm-camel text-sm mb-4">
                {filteredProducts.length} productos encontrados
              </p>
            </div>

            <MobileGrid cols={2} gap={3} className="px-4">
              <AnimatePresence mode="popLayout">
                {currentProducts.map((product, index) => (
                  <MobileProductCard
                    key={product.id}
                    product={product}
                    index={index}
                  />
                ))}
              </AnimatePresence>
            </MobileGrid>

            {/* Mobile Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center gap-2 p-4 mt-6">
                <MobileButton
                  variant="secondary"
                  disabled={currentPage === 1}
                  onClick={() => setCurrentPage(prev => prev - 1)}
                  size="sm"
                >
                  ←
                </MobileButton>

                <span className="flex items-center px-3 text-sm text-warm-camel">
                  {currentPage} de {totalPages}
                </span>

                <MobileButton
                  variant="secondary"
                  disabled={currentPage === totalPages}
                  onClick={() => setCurrentPage(prev => prev + 1)}
                  size="sm"
                >
                  →
                </MobileButton>
              </div>
            )}

            {/* No Results - Mobile */}
            {filteredProducts.length === 0 && (
              <div className="text-center py-12 px-4">
                <div className="text-6xl mb-4">👟</div>
                <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                  No se encontraron productos
                </h3>
                <p className="text-warm-camel mb-4 text-sm">
                  Intenta ajustar tus filtros o buscar algo diferente
                </p>
                <MobileButton variant="primary" onClick={clearFilters}>
                  Limpiar Filtros
                </MobileButton>
              </div>
            )}
          </MobileContainer>
        </MobileContainer>

        {/* Mobile Filters Bottom Sheet */}
        <MobileBottomSheet
          isOpen={isMobileFiltersOpen}
          onClose={() => setIsMobileFiltersOpen(false)}
          title="Filtros"
        >
          <div className="p-6 space-y-6">
            {/* Categories */}
            <div>
              <h4 className="font-medium text-forest-emerald dark:text-light-cloud-gray mb-3">
                Categorías
              </h4>
              <div className="grid grid-cols-2 gap-2">
                {categories.map(category => (
                  <MobileButton
                    key={category.id}
                    variant={filters.category === category.id ? 'primary' : 'secondary'}
                    onClick={() => handleFilterChange('category', category.id)}
                    size="sm"
                    className="justify-start"
                  >
                    {category.icon} {category.name}
                  </MobileButton>
                ))}
              </div>
            </div>

            {/* Brands */}
            <div>
              <h4 className="font-medium text-forest-emerald dark:text-light-cloud-gray mb-3">
                Marcas
              </h4>
              <div className="grid grid-cols-2 gap-2">
                {brands.map(brand => (
                  <MobileButton
                    key={brand.id}
                    variant={filters.brand === brand.id ? 'primary' : 'secondary'}
                    onClick={() => handleFilterChange('brand', brand.id)}
                    size="sm"
                  >
                    {brand.name}
                  </MobileButton>
                ))}
              </div>
            </div>

            {/* Special Filters */}
            <div>
              <h4 className="font-medium text-forest-emerald dark:text-light-cloud-gray mb-3">
                Especiales
              </h4>
              <div className="space-y-2">
                <MobileButton
                  variant={filters.isLimited ? 'primary' : 'secondary'}
                  onClick={() => handleFilterChange('isLimited', !filters.isLimited)}
                  fullWidth
                  size="sm"
                >
                  🔥 Edición Limitada
                </MobileButton>
                <MobileButton
                  variant={filters.isNew ? 'primary' : 'secondary'}
                  onClick={() => handleFilterChange('isNew', !filters.isNew)}
                  fullWidth
                  size="sm"
                >
                  ✨ Nuevos Lanzamientos
                </MobileButton>
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-3 pt-4 border-t border-white/10">
              <MobileButton
                variant="secondary"
                onClick={clearFilters}
                fullWidth
              >
                Limpiar
              </MobileButton>
              <MobileButton
                variant="primary"
                onClick={() => setIsMobileFiltersOpen(false)}
                fullWidth
              >
                Aplicar
              </MobileButton>
            </div>
          </div>
        </MobileBottomSheet>

        {/* AI Search Modals */}
        <EnhancedVoiceSearch
          isOpen={isVoiceSearchOpen}
          onClose={() => setIsVoiceSearchOpen(false)}
          onResults={handleVoiceSearchResults}
        />

        <VisualSearch
          isOpen={isVisualSearchOpen}
          onClose={() => setIsVisualSearchOpen(false)}
          onSearch={handleVisualSearch}
        />
      </div>
    )
  }

  // Desktop Layout
  return (
    <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-4xl font-godber font-bold text-pure-black dark:text-pure-white mb-4">
            {filters.brand ?
              `Catálogo ${filters.brand.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}` :
              'Tienda'
            }
          </h1>
          <p className="text-text-gray text-lg font-poppins">
            {filters.brand ?
              `Todos los productos de ${filters.brand.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}` :
              'Descubre nuestra colección exclusiva de sneakers de lujo'
            }
          </p>
        </motion.div>

        {/* Search and Sort Bar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="flex flex-col sm:flex-row gap-4 mb-6"
        >
          {/* Enhanced Search */}
          <div className="flex-1">
            <EnhancedSearchBar
              placeholder="Buscar productos, marcas... 🎤 📸"
              onSearch={(searchTerm) => handleFilterChange('search', searchTerm)}
            />
          </div>

          {/* Sort */}
          <div className="flex gap-2">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="input-glass rounded-lg px-4 py-3 text-forest-emerald dark:text-light-cloud-gray focus:outline-none focus:ring-2 focus:ring-rich-gold"
            >
              <option value="newest">Más Recientes</option>
              <option value="price-low">Precio: Menor a Mayor</option>
              <option value="price-high">Precio: Mayor a Menor</option>
              <option value="name">Nombre A-Z</option>
              <option value="brand">Marca A-Z</option>
              <option value="rating">Mejor Valorados</option>
            </select>

            <Button
              variant={showFilters ? 'primary' : 'secondary'}
              onClick={() => setShowFilters(!showFilters)}
              className="relative"
            >
              Filtros
              {activeFiltersCount > 0 && (
                <Badge
                  variant="primary"
                  size="sm"
                  className="absolute -top-2 -right-2 min-w-[20px] h-5 flex items-center justify-center text-xs"
                >
                  {activeFiltersCount}
                </Badge>
              )}
            </Button>
          </div>
        </motion.div>

        <div className="flex gap-8">
          {/* Filters Sidebar */}
          <AnimatePresence>
            {showFilters && (
              <motion.div
                initial={{ opacity: 0, x: -300 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -300 }}
                className="w-80 space-y-6"
              >
                <Card variant="default">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray">
                        Filtros
                      </h3>
                      {activeFiltersCount > 0 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={clearFilters}
                        >
                          Limpiar
                        </Button>
                      )}
                    </div>

                    <div className="space-y-6">
                      {/* Categories */}
                      <div>
                        <h4 className="font-medium text-forest-emerald dark:text-light-cloud-gray mb-3">
                          Categorías
                        </h4>
                        <div className="space-y-2">
                          {categories.map(category => (
                            <label key={category.id} className="flex items-center gap-2 cursor-pointer">
                              <input
                                type="radio"
                                name="category"
                                value={category.id}
                                checked={filters.category === category.id}
                                onChange={(e) => handleFilterChange('category', e.target.value)}
                                className="text-rich-gold focus:ring-rich-gold"
                              />
                              <span className="text-sm text-warm-camel">
                                {category.icon} {category.name}
                              </span>
                            </label>
                          ))}
                        </div>
                      </div>

                      {/* Brands */}
                      <div>
                        <h4 className="font-medium text-forest-emerald dark:text-light-cloud-gray mb-3">
                          Marcas
                        </h4>
                        <div className="space-y-2">
                          {brands.map(brand => (
                            <label key={brand.id} className="flex items-center gap-2 cursor-pointer">
                              <input
                                type="radio"
                                name="brand"
                                value={brand.id}
                                checked={filters.brand === brand.id}
                                onChange={(e) => handleFilterChange('brand', e.target.value)}
                                className="text-rich-gold focus:ring-rich-gold"
                              />
                              <span className="text-sm text-warm-camel">
                                {brand.name}
                              </span>
                            </label>
                          ))}
                        </div>
                      </div>

                      {/* Gender */}
                      <div>
                        <h4 className="font-medium text-forest-emerald dark:text-light-cloud-gray mb-3">
                          Género
                        </h4>
                        <div className="space-y-2">
                          {['men', 'women', 'unisex'].map(gender => (
                            <label key={gender} className="flex items-center gap-2 cursor-pointer">
                              <input
                                type="radio"
                                name="gender"
                                value={gender}
                                checked={filters.gender === gender}
                                onChange={(e) => handleFilterChange('gender', e.target.value)}
                                className="text-rich-gold focus:ring-rich-gold"
                              />
                              <span className="text-sm text-warm-camel capitalize">
                                {gender === 'men' ? 'Hombre' : gender === 'women' ? 'Mujer' : 'Unisex'}
                              </span>
                            </label>
                          ))}
                        </div>
                      </div>

                      {/* Special Filters */}
                      <div>
                        <h4 className="font-medium text-forest-emerald dark:text-light-cloud-gray mb-3">
                          Especiales
                        </h4>
                        <div className="space-y-2">
                          <label className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="checkbox"
                              checked={filters.isLimited}
                              onChange={(e) => handleFilterChange('isLimited', e.target.checked)}
                              className="text-rich-gold focus:ring-rich-gold"
                            />
                            <span className="text-sm text-warm-camel">
                              Edición Limitada
                            </span>
                          </label>
                          <label className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="checkbox"
                              checked={filters.isNew}
                              onChange={(e) => handleFilterChange('isNew', e.target.checked)}
                              className="text-rich-gold focus:ring-rich-gold"
                            />
                            <span className="text-sm text-warm-camel">
                              Nuevos Lanzamientos
                            </span>
                          </label>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Products Grid */}
          <div className="flex-1">
            {/* Results Info */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="flex items-center justify-between mb-6"
            >
              <p className="text-warm-camel">
                Mostrando {indexOfFirstProduct + 1}-{Math.min(indexOfLastProduct, filteredProducts.length)} de {filteredProducts.length} productos
              </p>
            </motion.div>

            {/* Products Grid */}
            <motion.div
              layout
              className={`grid gap-6 mb-8 ${
                showFilters
                  ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
                  : 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
              }`}
            >
              <AnimatePresence mode="popLayout">
                {currentProducts.map((product, index) => (
                  <AnimatedProductCard
                    key={product.id}
                    product={product}
                    index={index}
                    onAuthRequired={() => setIsAuthModalOpen(true)}
                  />
                ))}
              </AnimatePresence>
            </motion.div>

            {/* Pagination */}
            {totalPages > 1 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="flex justify-center gap-2"
              >
                <Button
                  variant="secondary"
                  disabled={currentPage === 1}
                  onClick={() => setCurrentPage(prev => prev - 1)}
                >
                  Anterior
                </Button>
                
                {[...Array(totalPages)].map((_, index) => (
                  <Button
                    key={index + 1}
                    variant={currentPage === index + 1 ? 'primary' : 'ghost'}
                    onClick={() => setCurrentPage(index + 1)}
                  >
                    {index + 1}
                  </Button>
                ))}
                
                <Button
                  variant="secondary"
                  disabled={currentPage === totalPages}
                  onClick={() => setCurrentPage(prev => prev + 1)}
                >
                  Siguiente
                </Button>
              </motion.div>
            )}

            {/* No Results */}
            {filteredProducts.length === 0 && (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="text-center py-12"
              >
                <div className="text-6xl mb-4">👟</div>
                <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                  No se encontraron productos
                </h3>
                <p className="text-warm-camel mb-4">
                  Intenta ajustar tus filtros o buscar algo diferente
                </p>
                <Button variant="primary" onClick={clearFilters}>
                  Limpiar Filtros
                </Button>
              </motion.div>
            )}
          </div>
        </div>
      </div>

      {/* Floating AI Search Buttons */}
      <div className="fixed bottom-8 right-8 z-40 flex flex-col gap-3">
        <motion.button
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.5 }}
          onClick={() => setIsVoiceSearchOpen(true)}
          className="w-14 h-14 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center hover:scale-110 group"
          title="Búsqueda por Voz"
        >
          <svg className="w-6 h-6 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
          </svg>
        </motion.button>

        <motion.button
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.6 }}
          onClick={() => setIsVisualSearchOpen(true)}
          className="w-14 h-14 bg-gradient-to-r from-green-500 to-teal-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center hover:scale-110 group"
          title="Búsqueda Visual"
        >
          <svg className="w-6 h-6 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
        </motion.button>
      </div>

      {/* Auth Modal */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
      />

      {/* AI Search Modals */}
      <EnhancedVoiceSearch
        isOpen={isVoiceSearchOpen}
        onClose={() => setIsVoiceSearchOpen(false)}
        onResults={handleVoiceSearchResults}
      />

      <VisualSearch
        isOpen={isVisualSearchOpen}
        onClose={() => setIsVisualSearchOpen(false)}
        onSearch={handleVisualSearch}
      />
    </div>
  )
}
