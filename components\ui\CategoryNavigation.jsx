'use client'

import { useState } from 'react'
import { cn } from '@/lib/utils'

const categories = [
  { id: 'tennis', name: 'TENNIS', href: '/categories/tennis' },
  { id: 'sandalias', name: 'SANDALIAS', href: '/categories/sandalias' },
  { id: 'botas', name: 'BOTAS', href: '/categories/botas' },
  { id: 'tacones', name: 'TACONES', href: '/categories/tacones' },
  { id: 'casual', name: 'CASUAL', href: '/categories/casual' },
  { id: 'formal', name: 'FORMAL', href: '/categories/formal' }
]

export default function CategoryNavigation({ 
  activeCategory = 'sandalias',
  onCategoryChange,
  className 
}) {
  const [selectedCategory, setSelectedCategory] = useState(activeCategory)

  const handleCategoryClick = (categoryId) => {
    setSelectedCategory(categoryId)
    if (onCategoryChange) {
      onCategoryChange(categoryId)
    }
  }

  return (
    <div className={cn('w-full', className)}>
      {/* TWL Aesthetic Category Navigation - Clean & Centered */}
      <div className="flex justify-center mb-8">
        {/* Centered horizontal layout with TWL styling - Extra padding for hover effects */}
        <div className="flex items-center gap-3 sm:gap-4 lg:gap-6 overflow-x-auto scrollbar-hide px-4 py-6">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => handleCategoryClick(category.id)}
              className={cn(
                // TWL Button Design - Clean & Professional with Minimalist Shadow
                'flex-shrink-0 px-6 py-3 font-poppins font-medium transition-all duration-300 transform',
                'min-w-[90px] rounded-xl border-2 text-sm sm:text-base',
                'hover:scale-102 hover:shadow-sm',
                // Active state: Lime green with black text (TWL rule) - Minimalist shadow
                selectedCategory === category.id
                  ? 'bg-primary border-primary text-pure-black font-semibold shadow-sm'
                  : 'bg-pure-white border-text-gray/20 text-text-gray hover:border-primary/50 hover:text-pure-black dark:bg-neutral-800 dark:border-neutral-600 dark:text-neutral-300 dark:hover:border-primary'
              )}
            >
              <span className="whitespace-nowrap tracking-wide uppercase text-xs sm:text-sm font-semibold">
                {category.name}
              </span>
            </button>
          ))}
        </div>
      </div>
    </div>
  )
}
