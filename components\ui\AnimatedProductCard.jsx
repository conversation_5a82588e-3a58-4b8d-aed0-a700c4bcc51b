'use client'

import { useState } from 'react'
import Link from 'next/link'
import { motion, useSpring } from 'framer-motion'
import { useCart } from '@/contexts/CartContext'
import { useAuth } from '@/contexts/AuthContext'
import { Card, CardContent } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import WishlistButton from '@/components/ui/WishlistButton'
import { ShoppingCart } from 'lucide-react'
import { formatPrice } from '@/lib/utils'

export default function AnimatedProductCard({ product, index = 0, onAuthRequired }) {
  const { addItem } = useCart()
  const { isAuthenticated } = useAuth()
  const [isHovered, setIsHovered] = useState(false)
  const [isPressed, setIsPressed] = useState(false)
  const [selectedSize, setSelectedSize] = useState('')
  const [showSizeSelector, setShowSizeSelector] = useState(false)
  
  // Spring animations for smooth interactions
  const springConfig = { stiffness: 300, damping: 30 }
  const scale = useSpring(1, springConfig)
  const rotateX = useSpring(0, springConfig)
  const rotateY = useSpring(0, springConfig)

  const handleMouseMove = (e) => {
    if (!isHovered) return
    
    const rect = e.currentTarget.getBoundingClientRect()
    const centerX = rect.left + rect.width / 2
    const centerY = rect.top + rect.height / 2
    
    const mouseX = (e.clientX - centerX) / rect.width
    const mouseY = (e.clientY - centerY) / rect.height
    
    rotateX.set(mouseY * -10)
    rotateY.set(mouseX * 10)
  }

  const handleMouseEnter = () => {
    setIsHovered(true)
    scale.set(1.03)
  }

  const handleMouseLeave = () => {
    setIsHovered(false)
    scale.set(1)
    rotateX.set(0)
    rotateY.set(0)
  }

  const handleAddToCart = () => {
    if (!selectedSize) {
      setShowSizeSelector(true)
      return
    }

    addItem(product.id, selectedSize, 1)
    setShowSizeSelector(false)
    setSelectedSize('')

    // Show success feedback (in a real app, you'd use a toast notification)
    setIsPressed(true)
    setTimeout(() => setIsPressed(false), 200)
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ 
        duration: 0.6, 
        delay: index * 0.1,
        type: "spring",
        stiffness: 100
      }}
      style={{
        scale,
        rotateX,
        rotateY,
        transformStyle: "preserve-3d"
      }}
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      className="cursor-pointer"
    >
      <Card 
        variant="default" 
        className={`group relative overflow-hidden transition-all duration-500 ${
          isHovered ? 'shadow-2xl' : 'shadow-lg'
        }`}
      >
        <CardContent className="p-0">
          {/* Product Image Container */}
          <div className="relative aspect-square overflow-hidden">
            {/* Real Product Images with Hover Effect */}
            {product?.images && product.images.length > 0 ? (
              <div className="relative w-full h-full">
                {/* Main Image (Image 1) */}
                <motion.img
                  src={product.images[0]}
                  alt={product.name || 'Product Image'}
                  className="w-full h-full object-cover absolute inset-0"
                  initial={{ opacity: 1 }}
                  animate={{ opacity: isHovered && product.images[1] ? 0 : 1 }}
                  transition={{ duration: 0.3 }}
                  onError={(e) => {
                    e.target.style.display = 'none'
                  }}
                />

                {/* Hover Image (Image 2) */}
                {product.images[1] && (
                  <motion.img
                    src={product.images[1]}
                    alt={`${product.name} - Vista 2`}
                    className="w-full h-full object-cover absolute inset-0"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: isHovered ? 1 : 0 }}
                    transition={{ duration: 0.3 }}
                    onError={(e) => {
                      e.target.style.display = 'none'
                    }}
                  />
                )}

                {/* Removed green overlay to preserve real product colors */}
              </div>
            ) : (
              /* Fallback Placeholder */
              <motion.div
                className="w-full h-full bg-gradient-to-br from-light-gray to-text-gray flex items-center justify-center relative"
                animate={{
                  background: isHovered
                    ? "linear-gradient(135deg, #F8F9FA, #6B7280, #BFFF00)"
                    : "linear-gradient(135deg, #F8F9FA, #6B7280)"
                }}
                transition={{ duration: 0.5 }}
              >
                <span className="text-pure-black text-sm font-medium font-poppins">
                  {product?.name || 'Product Image'}
                </span>

                {/* Shimmer Effect */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-transparent via-lime-green/30 to-transparent"
                  initial={{ x: "-100%" }}
                  animate={{ x: isHovered ? "100%" : "-100%" }}
                  transition={{ duration: 0.8, ease: "easeInOut" }}
                />
              </motion.div>
            )}
            
            {/* Badges */}
            <div className="absolute top-3 left-3 flex flex-col gap-2">
              {product?.isLimited && (
                <motion.div
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
                >
                  <Badge variant="limited" size="sm" pulse>
                    Limitado
                  </Badge>
                </motion.div>
              )}
              {product?.isVip && (
                <motion.div
                  initial={{ scale: 0, rotate: 180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ delay: 0.4, type: "spring", stiffness: 200 }}
                >
                  <Badge variant="vip" size="sm">
                    VIP
                  </Badge>
                </motion.div>
              )}
            </div>

            {/* Quick Actions */}
            <motion.div 
              className="absolute top-3 right-3 flex flex-col gap-2"
              initial={{ opacity: 0, x: 20 }}
              animate={{ 
                opacity: isHovered ? 1 : 0,
                x: isHovered ? 0 : 20
              }}
              transition={{ duration: 0.3 }}
            >
              <motion.div
                whileHover={{ scale: 1.1, rotate: 5 }}
                whileTap={{ scale: 0.9 }}
              >
                <WishlistButton
                  productId={product?.id}
                  size="sm"
                  variant="secondary"
                  className="h-8 w-8"
                  onAuthRequired={onAuthRequired}
                />
              </motion.div>
            </motion.div>
          </div>

          {/* Product Info */}
          <motion.div 
            className="p-4 space-y-3"
            style={{ transform: "translateZ(20px)" }}
          >
            {/* Brand & Name */}
            <Link href={`/product/${product?.id}`}>
              <div className="cursor-pointer">
                <motion.p
                  className="text-sm text-text-gray font-medium font-poppins"
                  animate={{ opacity: isHovered ? 1 : 0.8 }}
                >
                  {product?.brand || 'Premium Brand'}
                </motion.p>
                <motion.h3
                  className="font-semibold text-pure-black dark:text-pure-white line-clamp-2 hover:text-lime-green transition-colors font-poppins"
                  animate={{
                    color: isHovered ? "#BFFF00" : "#000000"
                  }}
                  transition={{ duration: 0.3 }}
                >
                  {product?.name || 'Luxury Sneaker Collection'}
                </motion.h3>
              </div>
            </Link>

            {/* Price - Bigger and darker lime green */}
            <motion.div
              className="flex items-center gap-2"
              animate={{ scale: isHovered ? 1.05 : 1 }}
              transition={{ duration: 0.2 }}
            >
              <span className="text-xl font-bold text-lime-green-dark font-poppins">
                ${product?.price || 210}
              </span>
              {product?.originalPrice && (
                <span className="text-sm text-text-gray line-through font-poppins">
                  ${product.originalPrice}
                </span>
              )}
            </motion.div>

            {/* Size Selector */}
            {showSizeSelector && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="space-y-2"
              >
                <p className="text-xs text-warm-camel">Selecciona tu talla:</p>
                <div className="grid grid-cols-4 gap-1">
                  {(product?.sizes || ['36', '37', '38', '39', '40', '41', '42', '43']).slice(0, 8).map((size) => (
                    <button
                      key={size}
                      onClick={() => setSelectedSize(size)}
                      className={`text-xs py-1 px-2 rounded border transition-colors font-poppins ${
                        selectedSize === size
                          ? 'bg-lime-green text-pure-black border-lime-green'
                          : 'border-text-gray text-text-gray hover:border-lime-green hover:text-lime-green'
                      }`}
                    >
                      {size}
                    </button>
                  ))}
                </div>
              </motion.div>
            )}

            {/* Round Lime Green Cart Button - Bottom Right */}
            <div className="absolute bottom-4 right-4">
              <motion.button
                onClick={handleAddToCart}
                className="w-12 h-12 bg-lime-green rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: 0.2 }}
              >
                <ShoppingCart className="w-5 h-5 text-pure-black" />
              </motion.button>
            </div>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
