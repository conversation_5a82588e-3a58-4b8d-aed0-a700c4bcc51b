'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { ArrowLeft, Filter, Grid, List, Star, TrendingUp } from 'lucide-react'
import { getAllRealProducts, getRealProductsByCategory } from '@/lib/real-products-loader'
import { getCategoryById, filterProductsByCategory, sortCategoryProducts, searchInCategory } from '@/lib/category-utils'
import EnhancedProductCard from '@/components/ui/EnhancedProductCard'
import MobileProductCard from '@/components/ui/MobileProductCard'
import CategoryNavigation from '@/components/ui/CategoryNavigation'
import EnhancedSearchBar from '@/components/features/EnhancedSearchBar'
import { MobileContainer, MobileSection, MobileGrid } from '@/components/mobile/MobileContainer'

export default function SneakersPage() {
  const router = useRouter()
  const [products, setProducts] = useState([])
  const [filteredProducts, setFilteredProducts] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState('newest')
  const [viewMode, setViewMode] = useState('grid')
  const [selectedBrand, setSelectedBrand] = useState('all')
  const [priceRange, setPriceRange] = useState('all')
  const [isMobile, setIsMobile] = useState(false)

  const categoryInfo = getCategoryById('sneakers')

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024)
    }
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Load sneakers products
  useEffect(() => {
    const loadSneakersProducts = async () => {
      setIsLoading(true)
      try {
        const allProducts = getAllRealProducts()
        const sneakersProducts = filterProductsByCategory(allProducts, 'sneakers')
        
        console.log(`👟 Loaded ${sneakersProducts.length} sneakers products`)
        setProducts(sneakersProducts)
        setFilteredProducts(sneakersProducts)
      } catch (error) {
        console.error('Error loading sneakers products:', error)
        setProducts([])
        setFilteredProducts([])
      } finally {
        setIsLoading(false)
      }
    }

    loadSneakersProducts()
  }, [])

  // Apply filters and search
  useEffect(() => {
    let filtered = [...products]

    // Apply search
    if (searchTerm) {
      filtered = searchInCategory(products, 'sneakers', searchTerm)
    }

    // Apply brand filter
    if (selectedBrand !== 'all') {
      filtered = filtered.filter(product => 
        product.brand?.toLowerCase() === selectedBrand.toLowerCase()
      )
    }

    // Apply price range filter
    if (priceRange !== 'all') {
      const [min, max] = priceRange.split('-').map(Number)
      filtered = filtered.filter(product => {
        const price = product.price || 0
        return max ? (price >= min && price <= max) : price >= min
      })
    }

    // Apply sorting
    filtered = sortCategoryProducts(filtered, sortBy)

    setFilteredProducts(filtered)
  }, [products, searchTerm, sortBy, selectedBrand, priceRange])

  // Get unique brands for filter
  const availableBrands = [...new Set(products.map(p => p.brand).filter(Boolean))]

  // Mobile Layout
  if (isMobile) {
    return (
      <div className="min-h-screen bg-white dark:bg-gray-900 pt-16">
        <MobileContainer>
          {/* Mobile Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <button
              onClick={() => router.back()}
              className="p-2 text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <h1 className="text-lg font-bold text-black dark:text-white flex items-center gap-2">
              <span>👟</span>
              SNEAKERS
            </h1>
            <button
              onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
              className="p-2 text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white"
            >
              {viewMode === 'grid' ? <List className="w-5 h-5" /> : <Grid className="w-5 h-5" />}
            </button>
          </div>

          {/* Category Hero - Mobile */}
          <div className="p-4 bg-gradient-to-r from-blue-500/10 to-blue-600/5">
            <h2 className="text-2xl font-bold text-black dark:text-white mb-2">
              SNEAKERS DE LUJO
            </h2>
            <p className="text-gray-600 dark:text-gray-400 text-sm mb-3">
              Descubre nuestra exclusiva colección de sneakers de las mejores marcas del mundo
            </p>
            <div className="flex items-center gap-4 text-sm">
              <div className="flex items-center gap-1 text-lime-green-dark font-medium">
                <TrendingUp className="w-4 h-4" />
                <span>{filteredProducts.length} productos</span>
              </div>
              <div className="flex items-center gap-1 text-yellow-600">
                <Star className="w-4 h-4 fill-current" />
                <span>Edición Limitada</span>
              </div>
            </div>
          </div>

          {/* Mobile Search & Filters */}
          <div className="p-4 space-y-3">
            <EnhancedSearchBar
              placeholder="Buscar sneakers..."
              onSearch={setSearchTerm}
            />
            
            <div className="flex gap-2 overflow-x-auto scrollbar-hide">
              <select
                value={selectedBrand}
                onChange={(e) => setSelectedBrand(e.target.value)}
                className="px-3 py-2 text-xs border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              >
                <option value="all">Todas las marcas</option>
                {availableBrands.map(brand => (
                  <option key={brand} value={brand}>{brand}</option>
                ))}
              </select>
              
              <select
                value={priceRange}
                onChange={(e) => setPriceRange(e.target.value)}
                className="px-3 py-2 text-xs border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              >
                <option value="all">Todos los precios</option>
                <option value="0-3000">$0 - $3,000</option>
                <option value="3000-5000">$3,000 - $5,000</option>
                <option value="5000-10000">$5,000 - $10,000</option>
                <option value="10000">$10,000+</option>
              </select>
            </div>
          </div>

          {/* Mobile Products Grid */}
          <MobileSection title="" className="px-4">
            {isLoading ? (
              <div className="grid grid-cols-2 gap-4">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="bg-gray-200 dark:bg-gray-700 rounded-lg h-64 animate-pulse" />
                ))}
              </div>
            ) : (
              <MobileGrid cols={2} gap={4}>
                <AnimatePresence mode="popLayout">
                  {filteredProducts.map((product, index) => (
                    <MobileProductCard
                      key={product.id}
                      product={product}
                      index={index}
                    />
                  ))}
                </AnimatePresence>
              </MobileGrid>
            )}

            {/* No Results */}
            {!isLoading && filteredProducts.length === 0 && (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">👟</div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  No se encontraron sneakers
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Intenta ajustar tus filtros de búsqueda
                </p>
                <button
                  onClick={() => {
                    setSearchTerm('')
                    setSelectedBrand('all')
                    setPriceRange('all')
                  }}
                  className="bg-lime-green text-black px-6 py-3 rounded-lg hover:bg-lime-green/90 transition-colors"
                >
                  Limpiar filtros
                </button>
              </div>
            )}
          </MobileSection>
        </MobileContainer>
      </div>
    )
  }

  // Desktop Layout
  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Back Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <button
            onClick={() => router.back()}
            className="flex items-center gap-2 text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Volver</span>
          </button>
        </motion.div>

        {/* Sneakers Hero */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-8 p-8 bg-gradient-to-r from-blue-500/10 to-blue-600/5 rounded-2xl"
        >
          <div className="flex items-center gap-4 mb-4">
            <div className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white text-2xl">
              👟
            </div>
            <div>
              <h1 className="text-4xl font-bold text-black dark:text-white">
                SNEAKERS DE LUJO
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-400">
                Descubre nuestra exclusiva colección de sneakers de las mejores marcas del mundo
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-6 text-sm">
            <div className="flex items-center gap-2 text-lime-green-dark font-semibold">
              <TrendingUp className="w-5 h-5" />
              <span>{filteredProducts.length} productos disponibles</span>
            </div>
            <div className="flex items-center gap-2 text-yellow-600">
              <Star className="w-5 h-5 fill-current" />
              <span>Ediciones Limitadas Disponibles</span>
            </div>
            <div className="text-gray-600 dark:text-gray-400">
              Marcas: Nike, Gucci, Off-White, Jordan
            </div>
          </div>
        </motion.div>

        {/* Category Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <CategoryNavigation
            activeCategory="sneakers"
            onCategoryChange={(category) => router.push(`/categories/${category}`)}
            showIcons={true}
          />
        </motion.div>

        {/* Search and Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="flex flex-col lg:flex-row gap-4 mb-8"
        >
          <div className="flex-1">
            <EnhancedSearchBar
              placeholder="Buscar sneakers por nombre, marca o modelo..."
              onSearch={setSearchTerm}
            />
          </div>
          
          <div className="flex gap-3">
            <select
              value={selectedBrand}
              onChange={(e) => setSelectedBrand(e.target.value)}
              className="px-4 py-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-lime-green"
            >
              <option value="all">Todas las marcas</option>
              {availableBrands.map(brand => (
                <option key={brand} value={brand}>{brand}</option>
              ))}
            </select>
            
            <select
              value={priceRange}
              onChange={(e) => setPriceRange(e.target.value)}
              className="px-4 py-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-lime-green"
            >
              <option value="all">Todos los precios</option>
              <option value="0-3000">$0 - $3,000 MXN</option>
              <option value="3000-5000">$3,000 - $5,000 MXN</option>
              <option value="5000-10000">$5,000 - $10,000 MXN</option>
              <option value="10000">$10,000+ MXN</option>
            </select>

            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-4 py-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-lime-green"
            >
              <option value="newest">Más Recientes</option>
              <option value="price-low">Precio: Menor a Mayor</option>
              <option value="price-high">Precio: Mayor a Menor</option>
              <option value="name">Nombre A-Z</option>
              <option value="brand">Marca A-Z</option>
            </select>

            <button
              onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
              className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white transition-colors"
            >
              {viewMode === 'grid' ? <List className="w-5 h-5" /> : <Grid className="w-5 h-5" />}
            </button>
          </div>
        </motion.div>

        {/* Products Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          {isLoading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="bg-gray-200 dark:bg-gray-700 rounded-lg h-96 animate-pulse" />
              ))}
            </div>
          ) : (
            <div className={`grid gap-6 ${
              viewMode === 'grid' 
                ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
                : 'grid-cols-1'
            }`}>
              <AnimatePresence mode="popLayout">
                {filteredProducts.map((product, index) => (
                  <EnhancedProductCard
                    key={product.id}
                    product={product}
                    index={index}
                    layout={viewMode}
                  />
                ))}
              </AnimatePresence>
            </div>
          )}

          {/* No Results */}
          {!isLoading && filteredProducts.length === 0 && (
            <div className="text-center py-16">
              <div className="text-8xl mb-6">👟</div>
              <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                No se encontraron sneakers
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
                No hay sneakers disponibles que coincidan con tus filtros de búsqueda.
              </p>
              <button
                onClick={() => {
                  setSearchTerm('')
                  setSelectedBrand('all')
                  setPriceRange('all')
                }}
                className="bg-lime-green text-black px-8 py-4 rounded-lg hover:bg-lime-green/90 transition-colors font-semibold"
              >
                Limpiar filtros
              </button>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  )
}
