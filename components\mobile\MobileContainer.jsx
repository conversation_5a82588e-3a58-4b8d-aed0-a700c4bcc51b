'use client'

/**
 * 🏢 ENTERPRISE-GRADE MOBILE CONTAINER SYSTEM
 * 
 * Responsive container components optimized for mobile-first experiences
 * Built with enterprise-grade performance and accessibility standards
 */

import { motion } from 'framer-motion'
import { forwardRef } from 'react'

// Mobile-specific container with enterprise optimizations
export const MobileContainer = forwardRef(({ 
  children, 
  className = '', 
  noPadding = false,
  enableGestures = true,
  ...props 
}, ref) => {
  return (
    <div 
      ref={ref}
      className={`
        ${noPadding ? '' : 'px-4 sm:px-6'} 
        ${enableGestures ? 'touch-manipulation' : ''}
        ${className}
      `}
      {...props}
    >
      {children}
    </div>
  )
})

MobileContainer.displayName = 'MobileContainer'

// Mobile section with enterprise-grade animations
export function MobileSection({ 
  children, 
  className = '', 
  title, 
  subtitle, 
  action,
  enableAnimation = true,
  delay = 0
}) {
  const content = (
    <section className={`py-6 ${className}`}>
      {(title || subtitle || action) && (
        <div className="flex items-center justify-between mb-4 px-4">
          <div>
            {title && (
              <h2 className="text-xl font-playfair font-bold text-pure-black dark:text-pure-white">
                {title}
              </h2>
            )}
            {subtitle && (
              <p className="text-sm text-text-gray mt-1">
                {subtitle}
              </p>
            )}
          </div>
          {action && (
            <div className="touch-target">
              {action}
            </div>
          )}
        </div>
      )}
      <div className="px-4">
        {children}
      </div>
    </section>
  )

  if (enableAnimation) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ 
          duration: 0.4, 
          ease: 'easeOut',
          delay: delay * 0.1
        }}
      >
        {content}
      </motion.div>
    )
  }

  return content
}

// Mobile grid with responsive breakpoints
export function MobileGrid({ 
  children, 
  cols = 2, 
  gap = 4, 
  className = '',
  enableStagger = true
}) {
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-2',
    3: 'grid-cols-3',
    4: 'grid-cols-4'
  }

  const gridGap = {
    2: 'gap-2',
    3: 'gap-3',
    4: 'gap-4',
    6: 'gap-6'
  }

  const content = (
    <div className={`grid ${gridCols[cols]} ${gridGap[gap]} ${className}`}>
      {children}
    </div>
  )

  if (enableStagger) {
    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        {content}
      </motion.div>
    )
  }

  return content
}

// Mobile card with glassmorphic design
export function MobileCard({ 
  children, 
  className = '', 
  padding = true, 
  glass = true,
  enableHover = true,
  onClick
}) {
  const cardContent = (
    <div
      className={`
        ${glass ? 'bg-white/90 dark:bg-black/90 backdrop-blur-sm' : 'bg-white dark:bg-gray-800'} 
        ${padding ? 'p-4' : ''} 
        rounded-2xl border border-white/10 dark:border-gray-700
        ${onClick ? 'cursor-pointer' : ''}
        ${className}
      `}
      onClick={onClick}
    >
      {children}
    </div>
  )

  if (enableHover && onClick) {
    return (
      <motion.div
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        transition={{ duration: 0.2 }}
      >
        {cardContent}
      </motion.div>
    )
  }

  return cardContent
}

// Mobile button with enterprise-grade touch optimization
export function MobileButton({ 
  children, 
  variant = 'primary', 
  size = 'md', 
  fullWidth = false,
  disabled = false,
  loading = false,
  icon,
  onClick,
  className = '',
  ...props 
}) {
  const variants = {
    primary: 'bg-lime-green text-black hover:bg-lime-green/90',
    secondary: 'bg-gray-200 dark:bg-gray-700 text-black dark:text-white hover:bg-gray-300 dark:hover:bg-gray-600',
    ghost: 'text-black dark:text-white hover:bg-black/10 dark:hover:bg-white/10',
    danger: 'bg-red-500 text-white hover:bg-red-600'
  }

  const sizes = {
    sm: 'px-3 py-2 text-sm min-h-[40px]',
    md: 'px-4 py-3 text-base min-h-[44px]',
    lg: 'px-6 py-4 text-lg min-h-[48px]'
  }

  return (
    <motion.button
      onClick={onClick}
      disabled={disabled || loading}
      className={`
        ${variants[variant]}
        ${sizes[size]}
        ${fullWidth ? 'w-full' : ''}
        ${disabled || loading ? 'opacity-50 cursor-not-allowed' : ''}
        rounded-xl font-medium transition-all duration-200
        flex items-center justify-center gap-2
        touch-manipulation min-w-[44px]
        ${className}
      `}
      whileHover={{ scale: disabled || loading ? 1 : 1.02 }}
      whileTap={{ scale: disabled || loading ? 1 : 0.98 }}
      {...props}
    >
      {loading ? (
        <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin" />
      ) : (
        <>
          {icon && <span>{icon}</span>}
          {children}
        </>
      )}
    </motion.button>
  )
}

// Mobile bottom sheet with enterprise-grade animations
export function MobileBottomSheet({ 
  isOpen, 
  onClose, 
  children, 
  title,
  enableBackdrop = true
}) {
  if (!isOpen) return null

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 z-50"
    >
      {/* Backdrop */}
      {enableBackdrop && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
          onClick={onClose}
        />
      )}
      
      {/* Bottom Sheet */}
      <motion.div
        initial={{ y: '100%' }}
        animate={{ y: 0 }}
        exit={{ y: '100%' }}
        transition={{ 
          type: 'spring', 
          damping: 25, 
          stiffness: 300 
        }}
        className="absolute bottom-0 left-0 right-0 bg-white/95 dark:bg-black/95 backdrop-blur-xl rounded-t-3xl max-h-[90vh] overflow-hidden"
      >
        {/* Handle */}
        <div className="flex justify-center pt-3 pb-2">
          <div className="w-12 h-1 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
        </div>
        
        {/* Header */}
        {title && (
          <div className="px-6 pb-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-black dark:text-white">
              {title}
            </h3>
          </div>
        )}
        
        {/* Content */}
        <div className="overflow-y-auto max-h-[calc(90vh-100px)] pb-safe-area-inset-bottom">
          {children}
        </div>
      </motion.div>
    </motion.div>
  )
}

// Mobile input with enterprise optimizations
export function MobileInput({
  label,
  error,
  className = '',
  ...props
}) {
  return (
    <div className="space-y-2">
      {label && (
        <label className="block text-sm font-medium text-black dark:text-white">
          {label}
        </label>
      )}
      <input
        className={`
          w-full px-4 py-3 rounded-xl border border-gray-300 dark:border-gray-600
          bg-white dark:bg-gray-800 text-black dark:text-white
          focus:outline-none focus:ring-2 focus:ring-lime-green focus:border-lime-green
          transition-all duration-200 text-base
          ${error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : ''}
          ${className}
        `}
        {...props}
      />
      {error && (
        <p className="text-sm text-red-500">{error}</p>
      )}
    </div>
  )
}

export default MobileContainer
