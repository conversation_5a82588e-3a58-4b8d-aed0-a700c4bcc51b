'use client'

import { useState, useEffect } from 'react'
import { useParams, useRouter } from 'next/navigation'
import SimpleProductCard from '@/components/ui/SimpleProductCard'

// Simple Category Mapping
const CATEGORIES = {
  'sneakers': { name: 'Sneakers', id: 'sneakers' },
  'sandals': { name: '<PERSON><PERSON><PERSON>', id: 'sandals' },
  'formal': { name: 'Formal', id: 'formal' },
  'casual': { name: 'Casual', id: 'casual' },
  'kids': { name: 'Niño<PERSON>', id: 'kids' }
}

export default function CategoryPage() {
  console.log('🚀🚀🚀🚀🚀 CATEGORY PAGE COMPONENT EXECUTING!')
  console.log('🚀🚀🚀 RUNNING ON:', typeof window === 'undefined' ? 'SERVER' : 'CLIENT')

  const params = useParams()
  const [products, setProducts] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)

  const categorySlug = params.category
  const categoryInfo = CATEGORIES[categorySlug]

  console.log('🚀🚀🚀 CATEGORY SLUG:', categorySlug)
  console.log('🚀🚀🚀 CATEGORY INFO:', categoryInfo)

  // Load products using Enterprise API
  useEffect(() => {
    const loadProducts = async () => {
      console.log('🔥🔥🔥 USEEFFECT TRIGGERED!')
      console.log('🔥🔥🔥 Category:', categorySlug)

      if (!categoryInfo) {
        console.log('❌ No category info found')
        setError('Category not found')
        setIsLoading(false)
        return
      }

      try {
        setIsLoading(true)
        console.log('📡 Fetching products from Enterprise API...')

        // Use the Enterprise API to get products
        const response = await fetch(`/api/enterprise/products?category=${categorySlug}&pageSize=20`)

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()
        console.log('📦 API Response:', data)

        if (data.success && data.products) {
          console.log(`✅ Loaded ${data.products.length} products for ${categorySlug}`)
          setProducts(data.products)
        } else {
          console.log('❌ API returned no products')
          setProducts([])
        }
      } catch (error) {
        console.error('❌ Error loading products:', error)
        setError(error.message)
        setProducts([])
      } finally {
        setIsLoading(false)
        console.log('✅ Loading completed')
      }
    }

    loadProducts()
  }, [categorySlug, categoryInfo])

  if (!categoryInfo) {
    return (
      <div className="min-h-screen bg-fog-black text-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Categoría no encontrada</h1>
          <button
            onClick={() => window.history.back()}
            className="px-6 py-2 bg-lime-green text-black rounded-lg hover:bg-lime-green/80"
          >
            Volver
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-fog-black text-white">
      {/* Header */}
      <div className="bg-mist-gray border-b border-frosted-overlay">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <h1 className="text-3xl font-bold text-lime-green">{categoryInfo.name}</h1>
          <p className="text-gray-400 mt-2">
            {isLoading ? 'Cargando productos...' : `${products.length} productos encontrados`}
          </p>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        {isLoading && (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-lime-green mx-auto"></div>
            <p className="mt-4 text-gray-400">Cargando productos...</p>
          </div>
        )}

        {error && (
          <div className="text-center py-12">
            <p className="text-red-400 mb-4">Error: {error}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-6 py-2 bg-lime-green text-black rounded-lg hover:bg-lime-green/80"
            >
              Reintentar
            </button>
          </div>
        )}

        {!isLoading && !error && products.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-400 mb-4">No se encontraron productos en esta categoría</p>
          </div>
        )}

        {!isLoading && !error && products.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {products.map((product) => (
              <SimpleProductCard key={product.id} product={product} />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

