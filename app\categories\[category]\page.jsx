'use client'

import { useState, useEffect } from 'react'
import { useParams, useRouter } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { ArrowLeft, Filter, Grid, List } from 'lucide-react'
import { getAllRealProducts } from '@/lib/real-products-loader'
import { filterProductsByCategory } from '@/lib/category-utils'
import EnhancedProductCard from '@/components/ui/EnhancedProductCard'
import MobileProductCard from '@/components/ui/MobileProductCard'
import CategoryNavigation from '@/components/ui/CategoryNavigation'
import EnhancedSearchBar from '@/components/features/EnhancedSearchBar'
import { MobileContainer, MobileSection, MobileGrid } from '@/components/mobile/MobileContainer'

// CYTTE Category Mapping
const CATEGORY_MAPPING = {
  'sneakers': {
    id: 'sneakers',
    name: 'Sneakers',
    displayName: 'SNEAKERS',
    description: 'Descubre nuestra exclusiva colección de sneakers de lujo',
    icon: '👟',
    cytteFolder: '1. SNEAKERS',
    hero: {
      title: 'SNEAKERS DE LUJO',
      subtitle: 'Colección exclusiva de las mejores marcas del mundo',
      image: '/images/categories/sneakers-hero.jpg'
    }
  },
  'sandals': {
    id: 'sandals', 
    name: 'Sandals',
    displayName: 'SANDALIAS',
    description: 'Sandalias de lujo para cada ocasión',
    icon: '🩴',
    cytteFolder: '2. SANDALS',
    hero: {
      title: 'SANDALIAS PREMIUM',
      subtitle: 'Comodidad y estilo en cada paso',
      image: '/images/categories/sandals-hero.jpg'
    }
  },
  'formal': {
    id: 'formal',
    name: 'Formal',
    displayName: 'FORMAL',
    description: 'Calzado formal de las mejores casas de moda',
    icon: '👔',
    cytteFolder: '3. FORMAL',
    hero: {
      title: 'CALZADO FORMAL',
      subtitle: 'Elegancia y sofisticación para ocasiones especiales',
      image: '/images/categories/formal-hero.jpg'
    }
  },
  'casual': {
    id: 'casual',
    name: 'Casual',
    displayName: 'CASUAL',
    description: 'Estilo casual con toque de lujo',
    icon: '👞',
    cytteFolder: '4. CASUAL',
    hero: {
      title: 'CASUAL LUXURY',
      subtitle: 'Comodidad diaria con estilo premium',
      image: '/images/categories/casual-hero.jpg'
    }
  },
  'kids': {
    id: 'kids',
    name: 'Kids',
    displayName: 'NIÑOS',
    description: 'Calzado de lujo para los más pequeños',
    icon: '👶',
    cytteFolder: '5. KIDS',
    hero: {
      title: 'KIDS COLLECTION',
      subtitle: 'Estilo y calidad para los más pequeños',
      image: '/images/categories/kids-hero.jpg'
    }
  }
}

export default function CategoryPage() {
  const params = useParams()
  const router = useRouter()
  const [products, setProducts] = useState([])
  const [filteredProducts, setFilteredProducts] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState('newest')
  const [viewMode, setViewMode] = useState('grid')
  const [showFilters, setShowFilters] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  const categorySlug = params.category
  const categoryInfo = CATEGORY_MAPPING[categorySlug]

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024)
    }
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Load products for this category
  useEffect(() => {
    const loadCategoryProducts = async () => {
      if (!categoryInfo) return

      setIsLoading(true)
      try {
        console.log(`🔍 Loading products for category: ${categoryInfo.name}`)

        // Use the enhanced category filtering from category-utils
        const allProducts = getAllRealProducts()
        const categoryProducts = filterProductsByCategory(allProducts, categoryInfo.id)

        console.log(`📂 Loaded ${categoryProducts.length} products for category: ${categoryInfo.name}`)
        console.log('📋 Category products:', categoryProducts.map(p => ({ id: p.id, name: p.name, category: p.category })))

        setProducts(categoryProducts)
        setFilteredProducts(categoryProducts)
      } catch (error) {
        console.error('Error loading category products:', error)
        setProducts([])
        setFilteredProducts([])
      } finally {
        setIsLoading(false)
      }
    }

    loadCategoryProducts()
  }, [categorySlug, categoryInfo])

  // Apply search and sorting
  useEffect(() => {
    let filtered = [...products]

    // Apply search
    if (searchTerm) {
      const search = searchTerm.toLowerCase()
      filtered = filtered.filter(product =>
        product.name?.toLowerCase().includes(search) ||
        product.brand?.toLowerCase().includes(search) ||
        product.description?.toLowerCase().includes(search)
      )
    }

    // Apply sorting
    switch (sortBy) {
      case 'price-low':
        filtered.sort((a, b) => (a.price || 0) - (b.price || 0))
        break
      case 'price-high':
        filtered.sort((a, b) => (b.price || 0) - (a.price || 0))
        break
      case 'name':
        filtered.sort((a, b) => (a.name || '').localeCompare(b.name || ''))
        break
      case 'brand':
        filtered.sort((a, b) => (a.brand || '').localeCompare(b.brand || ''))
        break
      default: // newest
        filtered.sort((a, b) => new Date(b.createdAt || 0) - new Date(a.createdAt || 0))
    }

    setFilteredProducts(filtered)
  }, [products, searchTerm, sortBy])

  // Handle category not found
  if (!categoryInfo) {
    return (
      <div className="min-h-screen bg-white dark:bg-gray-900 pt-20 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Categoría no encontrada
          </h1>
          <button
            onClick={() => router.push('/shop')}
            className="bg-lime-green text-black px-6 py-3 rounded-lg hover:bg-lime-green/90 transition-colors"
          >
            Volver a la tienda
          </button>
        </div>
      </div>
    )
  }

  // Mobile Layout
  if (isMobile) {
    return (
      <div className="min-h-screen bg-white dark:bg-gray-900 pt-16">
        <MobileContainer>
          {/* Mobile Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <button
              onClick={() => router.back()}
              className="p-2 text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <h1 className="text-lg font-bold text-black dark:text-white">
              {categoryInfo.displayName}
            </h1>
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="p-2 text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white"
            >
              <Filter className="w-5 h-5" />
            </button>
          </div>

          {/* Category Hero - Mobile */}
          <div className="p-4 bg-gradient-to-r from-lime-green/10 to-lime-green/5">
            <h2 className="text-2xl font-bold text-black dark:text-white mb-2">
              {categoryInfo.hero.title}
            </h2>
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              {categoryInfo.hero.subtitle}
            </p>
            <p className="text-lime-green-dark font-medium mt-2">
              {filteredProducts.length} productos disponibles
            </p>
          </div>

          {/* Mobile Search */}
          <div className="p-4">
            <EnhancedSearchBar
              placeholder={`Buscar en ${categoryInfo.displayName}...`}
              onSearch={setSearchTerm}
            />
          </div>

          {/* Mobile Products Grid */}
          <MobileSection title="" className="px-4">
            {isLoading ? (
              <div className="grid grid-cols-2 gap-4">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="bg-gray-200 dark:bg-gray-700 rounded-lg h-64 animate-pulse" />
                ))}
              </div>
            ) : (
              <MobileGrid cols={2} gap={4}>
                <AnimatePresence mode="popLayout">
                  {filteredProducts.map((product, index) => (
                    <MobileProductCard
                      key={product.id}
                      product={product}
                      index={index}
                    />
                  ))}
                </AnimatePresence>
              </MobileGrid>
            )}

            {/* No Results */}
            {!isLoading && filteredProducts.length === 0 && (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">{categoryInfo.icon}</div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  No se encontraron productos
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Intenta ajustar tu búsqueda
                </p>
                <button
                  onClick={() => setSearchTerm('')}
                  className="bg-lime-green text-black px-6 py-3 rounded-lg hover:bg-lime-green/90 transition-colors"
                >
                  Limpiar búsqueda
                </button>
              </div>
            )}
          </MobileSection>
        </MobileContainer>
      </div>
    )
  }

  // Desktop Layout
  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Back Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6"
        >
          <button
            onClick={() => router.back()}
            className="flex items-center gap-2 text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Volver</span>
          </button>
        </motion.div>

        {/* Category Hero */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-8 p-8 bg-gradient-to-r from-lime-green/10 to-lime-green/5 rounded-2xl"
        >
          <h1 className="text-4xl font-bold text-black dark:text-white mb-4">
            {categoryInfo.hero.title}
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 mb-4">
            {categoryInfo.hero.subtitle}
          </p>
          <p className="text-lime-green-dark font-semibold">
            {filteredProducts.length} productos disponibles
          </p>
        </motion.div>

        {/* Category Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <CategoryNavigation
            activeCategory={categorySlug}
            onCategoryChange={(category) => router.push(`/categories/${category}`)}
          />
        </motion.div>

        {/* Search and Controls */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="flex flex-col sm:flex-row gap-4 mb-8"
        >
          <div className="flex-1">
            <EnhancedSearchBar
              placeholder={`Buscar en ${categoryInfo.displayName}...`}
              onSearch={setSearchTerm}
            />
          </div>
          
          <div className="flex gap-2">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-4 py-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-lime-green"
            >
              <option value="newest">Más Recientes</option>
              <option value="price-low">Precio: Menor a Mayor</option>
              <option value="price-high">Precio: Mayor a Menor</option>
              <option value="name">Nombre A-Z</option>
              <option value="brand">Marca A-Z</option>
            </select>

            <button
              onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
              className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 text-gray-600 dark:text-gray-400 hover:text-black dark:hover:text-white transition-colors"
            >
              {viewMode === 'grid' ? <List className="w-5 h-5" /> : <Grid className="w-5 h-5" />}
            </button>
          </div>
        </motion.div>

        {/* Products Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          {isLoading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="bg-gray-200 dark:bg-gray-700 rounded-lg h-96 animate-pulse" />
              ))}
            </div>
          ) : (
            <div className={`grid gap-6 ${
              viewMode === 'grid' 
                ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
                : 'grid-cols-1'
            }`}>
              <AnimatePresence mode="popLayout">
                {filteredProducts.map((product, index) => (
                  <EnhancedProductCard
                    key={product.id}
                    product={product}
                    index={index}
                    layout={viewMode}
                  />
                ))}
              </AnimatePresence>
            </div>
          )}

          {/* No Results */}
          {!isLoading && filteredProducts.length === 0 && (
            <div className="text-center py-16">
              <div className="text-8xl mb-6">{categoryInfo.icon}</div>
              <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                No se encontraron productos
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
                No hay productos disponibles en esta categoría que coincidan con tu búsqueda.
              </p>
              <button
                onClick={() => setSearchTerm('')}
                className="bg-lime-green text-black px-8 py-4 rounded-lg hover:bg-lime-green/90 transition-colors font-semibold"
              >
                Limpiar búsqueda
              </button>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  )
}
