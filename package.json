{"name": "twl-ecommerce", "version": "0.1.0", "private": true, "type": "module", "description": "The White Laces - Luxury streetwear e-commerce platform with glassmorphic UI and Mexico-first strategy", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "generate:component": "node scripts/generate-component.js", "generate:page": "node scripts/generate-page.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "cypress run", "cypress:open": "cypress open", "type-check": "tsc --noEmit", "lint:fix": "next lint --fix", "format:check": "prettier --check .", "i18n:sync": "node scripts/i18n-sync.js", "optimize:videos": "node scripts/twl-video-optimizer-2024.js", "check:performance": "lighthouse http://localhost:3000 --output=html --output-path=./lighthouse-report.html", "prepare": "husky install", "postinstall": "husky install"}, "dependencies": {"cloudinary": "^1.41.0", "clsx": "^2.0.0", "firebase": "^10.15.0", "framer-motion": "^10.16.0", "i18next": "^23.7.0", "next": "^14.2.29", "next-i18next": "^15.2.0", "react": "^18", "react-dom": "^18", "react-i18next": "^13.5.0", "redis": "^4.6.0", "stripe": "^14.0.0", "swr": "^2.2.0", "tailwind-merge": "^2.2.0", "zustand": "^4.4.0"}, "devDependencies": {"@tailwindcss/forms": "^0.5.0", "@tailwindcss/typography": "^0.5.0", "@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^13.4.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "cypress": "^13.6.0", "eslint": "^8", "eslint-config-next": "14.0.4", "husky": "^8.0.0", "jest": "^29.7.0", "lint-staged": "^15.2.0", "postcss": "^8", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.0", "tailwindcss": "^3.3.0", "typescript": "^5"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"]}, "keywords": ["ecommerce", "luxury", "streetwear", "nextjs", "tailwindcss", "glassmorphism", "mexico", "latam"]}