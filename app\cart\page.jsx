'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import SectionHeader from '@/components/ui/SectionHeader'
import { Card, CardContent } from '@/components/ui/Card'

import Badge from '@/components/ui/Badge'
import SmartCartFeatures from '@/components/features/SmartCartFeatures'
import { useCart } from '@/contexts/CartContext'
import { useCartEnhancements } from '@/lib/enterprise/minimal/useCartEnhancements'

export default function CartPage() {
  const { items: cartItems, updateQuantity, removeItem, getTotal, getItemsCount } = useCart()
  const enhancements = useCartEnhancements(cartItems) // Optional enhancements
  const [isCheckingOut, setIsCheckingOut] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  // Handle loading state - cart is ready when cartItems is defined (even if empty)
  useEffect(() => {
    if (cartItems !== undefined) {
      setIsLoading(false)
    }
  }, [cartItems])

  const handleQuantityChange = (itemId, newQuantity) => {
    if (newQuantity === 0) {
      removeItem(itemId)
    } else {
      updateQuantity(itemId, newQuantity)
    }
  }

  const handleCheckout = () => {
    setIsCheckingOut(true)
    // Simulate checkout process
    setTimeout(() => {
      window.location.href = '/checkout'
    }, 1500)
  }

  // Loading state while cart is initializing
  if (isLoading) {
    return (
      <div className="min-h-screen bg-pure-white dark:bg-dark-gray pt-36">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-16">
            <SectionHeader
              title="TU CARRITO"
              subtitle="Cargando tu carrito..."
              align="center"
              size="lg"
            />
          </div>
          <div className="text-center py-16">
            <motion.div
              className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full mx-auto"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            />
          </div>
        </div>
      </div>
    )
  }

  if (cartItems.length === 0) {
    return (
      <div className="min-h-screen bg-pure-white dark:bg-dark-gray pt-36">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-16">
            <SectionHeader 
              title="TU CARRITO" 
              subtitle="Tu carrito de compras está vacío"
              align="center"
              size="lg"
            />
          </div>

          <div className="text-center py-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="max-w-md mx-auto"
            >
              <div className="text-8xl mb-6">🛒</div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">
                Tu carrito está vacío
              </h3>
              <p className="text-gray-600 mb-8">
                Descubre nuestra increíble colección de sneakers de lujo y encuentra tu par perfecto.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/tienda">
                  <button className="px-8 py-3 bg-pure-white border-2 border-primary text-pure-black font-poppins font-medium rounded-lg hover:bg-primary hover:text-pure-black transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-md">
                    Explorar Productos
                  </button>
                </Link>

                <Link href="/limited-editions">
                  <button className="px-8 py-3 bg-pure-white border-2 border-orange-500 text-pure-black font-poppins font-medium rounded-lg hover:bg-orange-500 hover:text-pure-black transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-md">
                    Ver Ediciones Limitadas
                  </button>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-pure-white dark:bg-dark-gray pt-36">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Page Header */}
        <div className="py-16">
          <SectionHeader 
            title="TU CARRITO" 
            subtitle={`${getItemsCount()} ${getItemsCount() === 1 ? 'producto' : 'productos'} en tu carrito`}
            align="center"
            size="lg"
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
          {/* Cart Items */}
          <div className="lg:col-span-2 space-y-6">
            <Card variant="default">
              <CardContent className="p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-6">Productos en tu carrito</h3>
                
                <div className="space-y-4">
                  {cartItems.map((item, index) => (
                    <motion.div
                      key={item.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="flex items-center gap-4 p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow"
                    >
                      {/* Product Image */}
                      <div className="w-20 h-20 bg-gray-100 rounded-lg flex items-center justify-center">
                        <span className="text-2xl">👟</span>
                      </div>
                      
                      {/* Product Info */}
                      <div className="flex-1">
                        <h4 className="font-semibold text-gray-800">{item.name}</h4>
                        <p className="text-sm text-gray-600">{item.brand}</p>
                        {item.selectedSize && (
                          <p className="text-sm text-gray-500">Talla: {item.selectedSize}</p>
                        )}
                        {item.bundleDiscount && (
                          <Badge variant="sale" size="sm" className="mt-1">Bundle Discount</Badge>
                        )}
                      </div>
                      
                      {/* Quantity Controls */}
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                          className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100 transition-colors"
                        >
                          -
                        </button>
                        <span className="w-8 text-center font-medium">{item.quantity}</span>
                        <button
                          onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                          className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-100 transition-colors"
                        >
                          +
                        </button>
                      </div>
                      
                      {/* Price */}
                      <div className="text-right">
                        <p className="font-bold text-gray-800">
                          ${(item.price * item.quantity).toLocaleString()} MXN
                        </p>
                        {item.originalPrice && item.originalPrice > item.price && (
                          <p className="text-sm line-through text-gray-500">
                            ${(item.originalPrice * item.quantity).toLocaleString()}
                          </p>
                        )}
                      </div>
                      
                      {/* Remove Button */}
                      <button
                        onClick={() => removeItem(item.id)}
                        className="text-red-500 hover:text-red-700 transition-colors p-2"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Enhanced: Product Recommendations */}
            {enhancements.hasRecommendations && (
              <Card variant="default">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-gray-800 mb-6 flex items-center gap-2">
                    <span>💡</span>
                    También te podría interesar
                  </h3>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {enhancements.recommendations.map((product) => (
                      <motion.div
                        key={product.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                        onClick={() => window.location.href = `/product/${product.id}`}
                      >
                        <div className="w-full h-32 bg-gray-100 rounded-lg mb-3 flex items-center justify-center">
                          <span className="text-3xl">👟</span>
                        </div>

                        <h4 className="font-semibold text-gray-800 text-sm mb-1">{product.name}</h4>
                        <p className="text-xs text-gray-600 mb-2">{product.brand}</p>

                        <div className="flex items-center justify-between">
                          <div>
                            <span className="font-bold text-gray-800">${product.price}</span>
                            {product.originalPrice && product.originalPrice > product.price && (
                              <span className="text-xs line-through text-gray-500 ml-2">
                                ${product.originalPrice}
                              </span>
                            )}
                          </div>

                          {product.isLimitedEdition && (
                            <Badge variant="sale" size="sm">Limitada</Badge>
                          )}
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Smart Cart Features */}
            <SmartCartFeatures />
          </div>

          {/* Order Summary */}
          <div className="space-y-6">
            <Card variant="default" className="sticky top-24">
              <CardContent className="p-6">
                <h3 className="text-xl font-bold text-gray-800 mb-6">Resumen del pedido</h3>
                
                <div className="space-y-4 mb-6">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Subtotal</span>
                    <span className="font-medium">${getTotal().toLocaleString()} MXN</span>
                  </div>

                  {/* Enhanced: Show savings if available */}
                  {enhancements.hasSavings && (
                    <div className="flex justify-between">
                      <span className="text-green-600">✨ Ahorros</span>
                      <span className="font-medium text-green-600">-${enhancements.totalSavings.toLocaleString()} MXN</span>
                    </div>
                  )}

                  <div className="flex justify-between">
                    <span className="text-gray-600">Envío</span>
                    <span className="font-medium text-[#BFFF00]">GRATIS</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="text-gray-600">Impuestos</span>
                    <span className="font-medium">${Math.round(getTotal() * 0.16).toLocaleString()} MXN</span>
                  </div>

                  <hr className="border-gray-200" />

                  <div className="flex justify-between text-lg font-bold">
                    <span>Total</span>
                    <span>${Math.round(getTotal() * 1.16).toLocaleString()} MXN</span>
                  </div>

                  {/* Enhanced: Show enhancement status */}
                  {enhancements.hasEnhancements && (
                    <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
                      <p className="text-sm text-green-800 flex items-center gap-2">
                        <span>✨</span>
                        {enhancements.enhancedItemsCount} productos con datos mejorados
                        {enhancements.limitedEditionCount > 0 && (
                          <span className="ml-2 px-2 py-1 bg-orange-100 text-orange-800 rounded text-xs">
                            {enhancements.limitedEditionCount} Edición Limitada
                          </span>
                        )}
                      </p>
                    </div>
                  )}
                </div>

                <div className="flex flex-col gap-4">
                  <button
                    onClick={handleCheckout}
                    disabled={isCheckingOut}
                    className="w-full px-8 py-4 bg-pure-white border-2 border-primary text-pure-black font-poppins font-semibold rounded-lg hover:bg-primary hover:text-pure-black transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 disabled:hover:bg-pure-white flex items-center justify-center gap-3"
                  >
                    {isCheckingOut ? (
                      <motion.div
                        className="w-5 h-5 border-2 border-pure-black border-t-transparent rounded-full"
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      />
                    ) : (
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                      </svg>
                    )}
                    {isCheckingOut ? 'Procesando...' : 'Proceder al Pago'}
                  </button>

                  <Link href="/tienda">
                    <button className="w-full px-8 py-4 bg-pure-white border-2 border-orange-500 text-pure-black font-poppins font-semibold rounded-lg hover:bg-orange-500 hover:text-pure-black transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-md">
                      Seguir Comprando
                    </button>
                  </Link>
                </div>

                {/* Security Badges */}
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <div className="flex items-center justify-center gap-4 text-sm text-gray-500">
                    <div className="flex items-center gap-1">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                      </svg>
                      <span>Pago Seguro</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span>Garantía</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Promo Code */}
            <Card variant="default">
              <CardContent className="p-6">
                <h4 className="font-semibold text-gray-800 mb-4">Código de descuento</h4>
                <div className="flex gap-2">
                  <input
                    type="text"
                    placeholder="Ingresa tu código"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#BFFF00] focus:border-transparent"
                  />
                  <button className="px-6 py-2 bg-pure-white border-2 border-primary text-pure-black font-poppins font-medium rounded-lg hover:bg-primary hover:text-pure-black transition-all duration-300 transform hover:scale-105">
                    Aplicar
                  </button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
